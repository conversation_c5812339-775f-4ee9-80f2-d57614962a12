CREATE TABLE IF NOT EXISTS notification_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    origin VARCHAR(50) NOT NULL CHECK (origin IN ('chat', 'crm')),
    origin_value INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE(origin, origin_value)
);

CREATE INDEX IF NOT EXISTS idx_notification_types_origin ON notification_types(origin);
CREATE INDEX IF NOT EXISTS idx_notification_types_origin_value ON notification_types(origin, origin_value);
