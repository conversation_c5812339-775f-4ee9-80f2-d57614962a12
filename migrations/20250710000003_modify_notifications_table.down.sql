-- Remove indexes
DROP INDEX IF EXISTS idx_notifications_notif_category_id;
DROP INDEX IF EXISTS idx_notifications_notif_type_id;
DROP INDEX IF EXISTS idx_notifications_read_at;
DROP INDEX IF EXISTS idx_notifications_origin;
DROP INDEX IF EXISTS idx_notifications_sso_id;

-- Remove new columns
ALTER TABLE notifications DROP COLUMN IF EXISTS extra;
ALTER TABLE notifications DROP COLUMN IF EXISTS origin;

-- Revert notif_type_id and notif_category_id back to UUID
ALTER TABLE notifications ALTER COLUMN notif_category_id TYPE UUID USING notif_category_id::UUID;
ALTER TABLE notifications ALTER COLUMN notif_type_id TYPE UUID USING notif_type_id::UUID;

-- Make click_action required again
ALTER TABLE notifications ALTER COLUMN click_action SET NOT NULL;

-- Rename sso_id back to user_id
ALTER TABLE notifications RENAME COLUMN sso_id TO user_id;
