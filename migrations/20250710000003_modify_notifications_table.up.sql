-- Rename user_id to sso_id
ALTER TABLE notifications RENAME COLUMN user_id TO sso_id;

-- Make click_action optional by removing NOT NULL constraint
ALTER TABLE notifications ALTER COLUMN click_action DROP NOT NULL;

-- Change notif_type_id and notif_category_id from UUID to VARCHAR
ALTER TABLE notifications ALTER COLUMN notif_type_id TYPE VARCHAR(255);
ALTER TABLE notifications ALTER COLUMN notif_category_id TYPE VARCHAR(255);

-- Add new columns
ALTER TABLE notifications ADD COLUMN origin VARCHAR(50) CHECK (origin IN ('chat', 'crm'));
ALTER TABLE notifications ADD COLUMN extra JSONB;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_sso_id ON notifications(sso_id);
CREATE INDEX IF NOT EXISTS idx_notifications_origin ON notifications(origin);
CREATE INDEX IF NOT EXISTS idx_notifications_read_at ON notifications(read_at);
CREATE INDEX IF NOT EXISTS idx_notifications_notif_type_id ON notifications(notif_type_id);
CREATE INDEX IF NOT EXISTS idx_notifications_notif_category_id ON notifications(notif_category_id);
