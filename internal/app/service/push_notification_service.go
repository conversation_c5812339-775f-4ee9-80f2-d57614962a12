package service

import (
	"context"
	"log/slog"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	"bitbucket.org/terbang-ventures/notification-service/internal/pkg/fcm"
	"bitbucket.org/terbang-ventures/notification-service/internal/pkg/notification"
	"github.com/google/uuid"
)

// PushNotificationService defines the interface for push notification service operations
type PushNotificationService interface {
	SendPushNotification(ctx context.Context, ssoIDs []uuid.UUID, data map[string]interface{}, notification map[string]string, clickAction string) error
}

type pushNotificationService struct {
	fcmTokenRepo        repository.FCMTokenRepository
	fcmService          fcm.FCMService
	notificationService NotificationService
}

// NewPushNotificationService creates a new push notification service
func NewPushNotificationService(fcmTokenRepo repository.FCMTokenRepository, fcmService fcm.FCMService, notificationService NotificationService) PushNotificationService {
	return &pushNotificationService{
		fcmTokenRepo:        fcmTokenRepo,
		fcmService:          fcmService,
		notificationService: notificationService,
	}
}

// SendPushNotification sends a push notification to the specified SSO IDs
func (s *pushNotificationService) SendPushNotification(ctx context.Context, ssoIDs []uuid.UUID, data map[string]interface{}, notification map[string]string, clickAction string) error {
	if len(ssoIDs) == 0 {
		return nil
	}

	// Parse FCM payload and create notification records (only if payload has required structure)
	// if notification.IsNotificationPayload(data) {
	// 	if err := s.createNotificationRecords(ctx, ssoIDs, data, clickAction); err != nil {
	// 		slog.ErrorContext(ctx, "Error creating notification records",
	// 			slog.Any("error", err),
	// 			slog.Any("sso_ids", ssoIDs),
	// 		)
	// 		// Continue with FCM sending even if notification creation fails
	// 	}
	// } else {
	// 	slog.DebugContext(ctx, "Payload does not contain required fields for automatic notification creation",
	// 		slog.Any("data", data),
	// 	)
	// }

	// Get FCM tokens for the specified SSO IDs
	tokens, err := s.fcmTokenRepo.FindBySSOs(ctx, ssoIDs)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting FCM tokens",
			slog.Any("error", err),
			slog.Any("sso_ids", ssoIDs),
		)
		return err
	}

	if len(tokens) == 0 {
		slog.InfoContext(ctx, "No FCM tokens found for the specified SSO IDs",
			slog.Any("sso_ids", ssoIDs),
		)
		return nil
	}

	// Group tokens by user source
	tokensBySource := make(map[model.UserSource][]string)
	for _, token := range tokens {
		tokensBySource[token.UserSource] = append(tokensBySource[token.UserSource], token.Token)
	}

	// Send notifications for each user source
	for source, sourceTokens := range tokensBySource {
		err := s.fcmService.SendNotification(ctx, sourceTokens, data, notification, clickAction, source)
		if err != nil {
			slog.ErrorContext(ctx, "Error sending FCM notification",
				slog.Any("error", err),
				slog.String("source", string(source)),
				slog.Int("token_count", len(sourceTokens)),
			)
			// Continue with other sources even if one fails
			continue
		}
	}

	return nil
}

// createNotificationRecords creates notification records based on FCM payload
func (s *pushNotificationService) createNotificationRecords(ctx context.Context, ssoIDs []uuid.UUID, data map[string]interface{}, clickAction string) error {
	// Parse the FCM payload
	payloadData, err := notification.ParseFCMPayload(data)
	if err != nil {
		slog.WarnContext(ctx, "Failed to parse FCM payload for notification creation",
			slog.Any("error", err),
			slog.Any("data", data),
		)
		return err
	}

	// Build description
	description := payloadData.BuildDescription()

	// Create notification records for each user
	notifications := make([]*model.Notification, 0, len(ssoIDs))
	for _, ssoID := range ssoIDs {
		notif := &model.Notification{
			UserID:         ssoID,
			Description:    description,
			ClickAction:    model.ClickActionOpenURL,
			ClickActionURL: clickAction,
			EventType:      payloadData.EventType,
			IsReminder:     false,
		}
		notifications = append(notifications, notif)
	}

	// Create notifications in batch
	if err := s.notificationService.CreateNotificationBatch(ctx, notifications); err != nil {
		slog.ErrorContext(ctx, "Failed to create notification batch",
			slog.Any("error", err),
			slog.Int("count", len(notifications)),
		)
		return err
	}

	slog.InfoContext(ctx, "Successfully created notification records",
		slog.Int("count", len(notifications)),
		slog.String("event_type", payloadData.EventType),
		slog.String("description", description),
	)

	return nil
}
