package service

import (
	"context"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationService defines the interface for notification service operations
type NotificationService interface {
	CreateNotification(ctx context.Context, notification *model.Notification) error
	CreateNotificationBatch(ctx context.Context, notifications []*model.Notification) error
	GetNotificationByID(ctx context.Context, id uuid.UUID) (*model.NotificationResponse, error)
	GetNotificationsBySSO(ctx context.Context, ssoID uuid.UUID, page, limit int) ([]model.NotificationResponse, int64, error)
	GetNotifications(ctx context.Context, page, limit int) ([]model.NotificationResponse, int64, error)
	MarkAsRead(ctx context.Context, id uuid.UUID) error
	MarkAllAsReadBySSO(ctx context.Context, ssoID uuid.UUID) error
}

type notificationService struct {
	notificationRepo repository.NotificationRepository
}

// NewNotificationService creates a new notification service
func NewNotificationService(notificationRepo repository.NotificationRepository) NotificationService {
	return &notificationService{
		notificationRepo: notificationRepo,
	}
}

// CreateNotification creates a new notification
func (s *notificationService) CreateNotification(ctx context.Context, notification *model.Notification) error {
	return s.notificationRepo.Create(ctx, notification)
}

// CreateNotificationBatch creates multiple notifications
func (s *notificationService) CreateNotificationBatch(ctx context.Context, notifications []*model.Notification) error {
	return s.notificationRepo.CreateBatch(ctx, notifications)
}

// GetNotificationByID gets a notification by ID
func (s *notificationService) GetNotificationByID(ctx context.Context, id uuid.UUID) (*model.NotificationResponse, error) {
	notification, err := s.notificationRepo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, httpLib.NewRecordNotFoundException("Notification")
		}
		return nil, err
	}
	response := notification.ToResponse()
	return &response, nil
}

// GetNotificationsBySSO gets notifications by SSO ID with pagination
func (s *notificationService) GetNotificationsBySSO(ctx context.Context, ssoID uuid.UUID, page, limit int) ([]model.NotificationResponse, int64, error) {
	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10 // Default limit
	}

	notifications, total, err := s.notificationRepo.FindBySSO(ctx, ssoID, page, limit)
	if err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]model.NotificationResponse, len(notifications))
	for i, notification := range notifications {
		responses[i] = notification.ToResponse()
	}

	return responses, total, nil
}

// GetNotifications gets all notifications with pagination
func (s *notificationService) GetNotifications(ctx context.Context, page, limit int) ([]model.NotificationResponse, int64, error) {
	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10 // Default limit
	}

	notifications, total, err := s.notificationRepo.FindAll(ctx, page, limit)
	if err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]model.NotificationResponse, len(notifications))
	for i, notification := range notifications {
		responses[i] = notification.ToResponse()
	}

	return responses, total, nil
}

// MarkAsRead marks a notification as read
func (s *notificationService) MarkAsRead(ctx context.Context, id uuid.UUID) error {
	return s.notificationRepo.MarkAsRead(ctx, id)
}

// MarkAllAsReadBySSO marks all notifications as read for a specific SSO ID
func (s *notificationService) MarkAllAsReadBySSO(ctx context.Context, ssoID uuid.UUID) error {
	return s.notificationRepo.MarkAllAsReadBySSO(ctx, ssoID)
}
