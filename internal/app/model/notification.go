package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ClickAction represents the possible actions when a notification is clicked
type ClickAction string

const (
	// Define possible click actions
	ClickActionOpenURL ClickAction = "OPEN_URL"
	ClickActionOpenApp ClickAction = "OPEN_APP"
)

// Notification represents a notification in the system
type Notification struct {
	ID              uuid.UUID   `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	SSOID           uuid.UUID   `json:"sso_id" gorm:"type:uuid;not null;column:sso_id"`
	Description     string      `json:"description" gorm:"type:text;not null"`
	ClickAction     ClickAction `json:"click_action" gorm:"type:varchar(50)"`
	ClickActionURL  string      `json:"click_action_url" gorm:"type:text"`
	ReadAt          *time.Time  `json:"read_at" gorm:"type:timestamp"`
	IsReminder      bool        `json:"is_reminder" gorm:"type:boolean;default:false"`
	NotifTypeID     string      `json:"notif_type_id" gorm:"type:varchar(255)"`
	NotifCategoryID string      `json:"notif_category_id" gorm:"type:varchar(255)"`
	CreatedAt       time.Time   `json:"created_at" gorm:"type:timestamp;not null;default:now()"`
	UpdatedAt       time.Time   `json:"updated_at" gorm:"type:timestamp;not null;default:now()"`
	Origin          string      `json:"origin" gorm:"type:varchar(50);check:origin IN ('chat', 'crm')"`
	Extra           interface{} `json:"extra" gorm:"type:jsonb"`
}

// TableName specifies the table name for the Notification model
func (Notification) TableName() string {
	return "notifications"
}

// BeforeCreate is a hook that runs before creating a new notification
func (n *Notification) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	n.CreatedAt = time.Now()
	n.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate is a hook that runs before updating a notification
func (n *Notification) BeforeUpdate(tx *gorm.DB) error {
	n.UpdatedAt = time.Now()
	return nil
}

// NotificationResponse represents the response format for notifications
type NotificationResponse struct {
	ID              uuid.UUID   `json:"id"`
	Description     string      `json:"description"`
	ClickAction     ClickAction `json:"click_action"`
	ClickActionURL  string      `json:"click_action_url"`
	IsRead          bool        `json:"is_read"`
	IsReminder      bool        `json:"is_reminder"`
	NotifTypeID     string      `json:"notif_type_id"`
	NotifCategoryID string      `json:"notif_category_id"`
	CreatedAt       time.Time   `json:"created_at"`
	Origin          string      `json:"origin"`
	Extra           interface{} `json:"extra"`
}

// ToResponse converts a Notification to NotificationResponse
func (n *Notification) ToResponse() NotificationResponse {
	return NotificationResponse{
		ID:              n.ID,
		Description:     n.Description,
		ClickAction:     n.ClickAction,
		ClickActionURL:  n.ClickActionURL,
		IsRead:          n.ReadAt != nil,
		IsReminder:      n.IsReminder,
		NotifTypeID:     n.NotifTypeID,
		NotifCategoryID: n.NotifCategoryID,
		CreatedAt:       n.CreatedAt,
		Origin:          n.Origin,
		Extra:           n.Extra,
	}
}
