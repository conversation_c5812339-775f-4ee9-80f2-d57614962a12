package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Origin represents the origin of the notification type
type Origin string

const (
	OriginChat Origin = "chat"
	OriginCRM  Origin = "crm"
)

// NotificationType represents a notification type in the system
type NotificationType struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Origin      Origin    `json:"origin" gorm:"type:varchar(50);not null;check:origin IN ('chat', 'crm')"`
	OriginValue int       `json:"origin_value" gorm:"type:integer;not null"`
	CreatedAt   time.Time `json:"created_at" gorm:"type:timestamp;not null;default:now()"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"type:timestamp;not null;default:now()"`
}

// TableName specifies the table name for the NotificationType model
func (NotificationType) TableName() string {
	return "notification_types"
}

// BeforeCreate is a hook that runs before creating a new notification type
func (nt *NotificationType) BeforeCreate(tx *gorm.DB) error {
	if nt.ID == uuid.Nil {
		nt.ID = uuid.New()
	}
	nt.CreatedAt = time.Now()
	nt.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate is a hook that runs before updating a notification type
func (nt *NotificationType) BeforeUpdate(tx *gorm.DB) error {
	nt.UpdatedAt = time.Now()
	return nil
}
