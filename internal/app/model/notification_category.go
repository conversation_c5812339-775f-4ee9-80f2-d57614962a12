package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationCategory represents a notification category in the system
type NotificationCategory struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Origin      Origin    `json:"origin" gorm:"type:varchar(50);not null;check:origin IN ('chat', 'crm')"`
	OriginValue int       `json:"origin_value" gorm:"type:integer;not null"`
	CreatedAt   time.Time `json:"created_at" gorm:"type:timestamp;not null;default:now()"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"type:timestamp;not null;default:now()"`
}

// TableName specifies the table name for the NotificationCategory model
func (NotificationCategory) TableName() string {
	return "notification_categories"
}

// BeforeCreate is a hook that runs before creating a new notification category
func (nc *NotificationCategory) BeforeCreate(tx *gorm.DB) error {
	if nc.ID == uuid.Nil {
		nc.ID = uuid.New()
	}
	nc.CreatedAt = time.Now()
	nc.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate is a hook that runs before updating a notification category
func (nc *NotificationCategory) BeforeUpdate(tx *gorm.DB) error {
	nc.UpdatedAt = time.Now()
	return nil
}
