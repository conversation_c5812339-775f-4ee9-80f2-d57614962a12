package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
)

// NotificationHandler defines the interface for notification handler operations
type NotificationHandler interface {
	CreateNotification(w http.ResponseWriter, r *http.Request)
	GetNotificationByID(w http.ResponseWriter, r *http.Request)
	GetNotifications(w http.ResponseWriter, r *http.Request)
	MarkAsRead(w http.ResponseWriter, r *http.Request)
	MarkAllAsRead(w http.ResponseWriter, r *http.Request)
}

type notificationHandler struct {
	notificationService service.NotificationService
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(notificationService service.NotificationService) NotificationHandler {
	return &notificationHandler{
		notificationService: notificationService,
	}
}

// CreateNotificationRequest represents the request body for creating a notification
type CreateNotificationRequest struct {
	Description     string            `json:"description" validate:"required" example:"You have a new message" description:"Content of the notification"`
	ClickAction     model.ClickAction `json:"click_action" example:"OPEN_URL" description:"Action to be performed when notification is clicked"`
	ClickActionURL  string            `json:"click_action_url" example:"https://example.com/message/123" description:"URL to open when notification is clicked"`
	IsReminder      bool              `json:"is_reminder" example:"false" description:"Whether this notification is a reminder"`
	NotifTypeID     string            `json:"notif_type_id" example:"type_123" description:"Type of notification"`
	NotifCategoryID string            `json:"notif_category_id" example:"category_123" description:"Category of notification"`
	Origin          string            `json:"origin" example:"chat" description:"Origin of the notification (chat/crm)"`
	Extra           interface{}       `json:"extra" example:"{\"key\":\"value\"}" description:"Additional data in JSON format"`
}

// @Summary Create a new notification
// @Description Create a new notification for a specific user
// @Tags notifications
// @Accept json
// @Produce json
// @Param body body CreateNotificationRequest true "Notification details"
// @Success 201 {object} domains.Response{data=model.NotificationResponse} "Notification created successfully"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid request body"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications [post]
func (h *notificationHandler) CreateNotification(w http.ResponseWriter, r *http.Request) {
	// Get SSO ID from middleware
	ssoIDStr, ok := r.Context().Value("ssoID").(string)
	if !ok || ssoIDStr == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID not found in context"}})
		return
	}

	ssoID, err := uuid.Parse(ssoIDStr)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	var req CreateNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid request body"}})
		return
	}

	notification := &model.Notification{
		SSOID:           ssoID,
		Description:     req.Description,
		ClickAction:     req.ClickAction,
		ClickActionURL:  req.ClickActionURL,
		IsReminder:      req.IsReminder,
		NotifTypeID:     req.NotifTypeID,
		NotifCategoryID: req.NotifCategoryID,
		Origin:          req.Origin,
		Extra:           req.Extra,
	}

	if err := h.notificationService.CreateNotification(r.Context(), notification); err != nil {
		httpLib.Error(w, err)
		return
	}

	response := notification.ToResponse()
	httpLib.WriteSuccessResponse(w, http.StatusCreated, response, nil)
}

// @Summary Get notification by ID
// @Description Retrieve a specific notification by its ID
// @Tags notifications
// @Produce json
// @Param id path string true "Notification ID (UUID)" format(uuid)
// @Success 200 {object} domains.Response{data=model.Notification} "Notification details"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid notification ID"
// @Failure 404 {object} domains.Response{errors=[]domains.ErrorDetail} "Notification not found"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications/{id} [get]
func (h *notificationHandler) GetNotificationByID(w http.ResponseWriter, r *http.Request) {
	idParam := chi.URLParam(r, "id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid notification ID"}})
		return
	}

	notification, err := h.notificationService.GetNotificationByID(r.Context(), id)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, notification, nil)
}

// @Summary Get all notifications
// @Description Retrieve a paginated list of notifications for the authenticated user
// @Tags notifications
// @Produce json
// @Param page query integer false "Page number (default: 1)" minimum(1)
// @Param limit query integer false "Number of items per page (default: 10, max: 100)" minimum(1) maximum(100)
// @Success 200 {object} domains.Response{data=[]model.NotificationResponse,meta=domains.PaginationMeta} "List of notifications with pagination metadata"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications [get]
func (h *notificationHandler) GetNotifications(w http.ResponseWriter, r *http.Request) {
	// Get SSO ID from middleware
	ssoIDStr, ok := r.Context().Value("ssoID").(string)
	if !ok || ssoIDStr == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID not found in context"}})
		return
	}

	ssoID, err := uuid.Parse(ssoIDStr)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID format"}})
		return
	}

	// Parse pagination parameters
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	notifications, total, err := h.notificationService.GetNotificationsBySSO(r.Context(), ssoID, page, limit)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	// Create pagination metadata
	meta := map[string]interface{}{
		"page":       page,
		"limit":      limit,
		"total":      total,
		"total_page": (total + int64(limit) - 1) / int64(limit),
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, notifications, meta)
}
