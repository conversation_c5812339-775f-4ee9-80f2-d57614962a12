package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// MockNotificationService is a mock implementation of service.NotificationService
type MockNotificationService struct {
	mock.Mock
}

// CreateNotification mocks the CreateNotification method
func (m *MockNotificationService) CreateNotification(ctx context.Context, notification *model.Notification) error {
	args := m.Called(ctx, notification)
	return args.Error(0)
}

// CreateNotificationBatch mocks the CreateNotificationBatch method
func (m *MockNotificationService) CreateNotificationBatch(ctx context.Context, notifications []*model.Notification) error {
	args := m.Called(ctx, notifications)
	return args.Error(0)
}

// GetNotificationByID mocks the GetNotificationByID method
func (m *MockNotificationService) GetNotificationByID(ctx context.Context, id uuid.UUID) (*model.NotificationResponse, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NotificationResponse), args.Error(1)
}

// GetNotificationsBySSO mocks the GetNotificationsBySSO method
func (m *MockNotificationService) GetNotificationsBySSO(ctx context.Context, ssoID uuid.UUID, page, limit int) ([]model.NotificationResponse, int64, error) {
	args := m.Called(ctx, ssoID, page, limit)
	return args.Get(0).([]model.NotificationResponse), args.Get(1).(int64), args.Error(2)
}

// GetNotifications mocks the GetNotifications method
func (m *MockNotificationService) GetNotifications(ctx context.Context, page, limit int) ([]model.NotificationResponse, int64, error) {
	args := m.Called(ctx, page, limit)
	return args.Get(0).([]model.NotificationResponse), args.Get(1).(int64), args.Error(2)
}

// MarkAsRead mocks the MarkAsRead method
func (m *MockNotificationService) MarkAsRead(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// MarkAllAsReadBySSO mocks the MarkAllAsReadBySSO method
func (m *MockNotificationService) MarkAllAsReadBySSO(ctx context.Context, ssoID uuid.UUID) error {
	args := m.Called(ctx, ssoID)
	return args.Error(0)
}

// NotificationHandlerTestSuite is a test suite for NotificationHandler
type NotificationHandlerTestSuite struct {
	suite.Suite
	mockService  *MockNotificationService
	handler      NotificationHandler
	notification *model.Notification
	ctx          context.Context
}

// SetupTest sets up the test suite
func (suite *NotificationHandlerTestSuite) SetupTest() {
	suite.mockService = new(MockNotificationService)
	suite.handler = NewNotificationHandler(suite.mockService)
	suite.notification = &model.Notification{
		ID:              uuid.New(),
		SSOID:           uuid.New(),
		Description:     "Test notification",
		ClickAction:     model.ClickActionOpenURL,
		ClickActionURL:  "https://example.com",
		IsReminder:      false,
		NotifTypeID:     "type_123",
		NotifCategoryID: "category_123",
		Origin:          "chat",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
	suite.ctx = context.Background()
}

// TestCreateNotification tests the CreateNotification method
func (suite *NotificationHandlerTestSuite) TestCreateNotification() {
	// Setup
	reqBody := CreateNotificationRequest{
		Description:     suite.notification.Description,
		ClickAction:     suite.notification.ClickAction,
		ClickActionURL:  suite.notification.ClickActionURL,
		IsReminder:      suite.notification.IsReminder,
		NotifTypeID:     suite.notification.NotifTypeID,
		NotifCategoryID: suite.notification.NotifCategoryID,
		Origin:          suite.notification.Origin,
	}
	reqBodyBytes, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewReader(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	suite.mockService.On("CreateNotification", req.Context(), mock.AnythingOfType("*model.Notification")).
		Run(func(args mock.Arguments) {
			notification := args.Get(1).(*model.Notification)
			notification.ID = suite.notification.ID
			notification.CreatedAt = suite.notification.CreatedAt
			notification.UpdatedAt = suite.notification.UpdatedAt
		}).
		Return(nil)

	// Execute
	suite.handler.CreateNotification(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])

	suite.mockService.AssertExpectations(suite.T())
}

// TestCreateNotification_InvalidJSON tests the CreateNotification method with invalid JSON
func (suite *NotificationHandlerTestSuite) TestCreateNotification_InvalidJSON() {
	// Setup
	reqBodyBytes := []byte(`{invalid json}`)
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewReader(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Execute
	suite.handler.CreateNotification(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestCreateNotification_ServiceError tests the CreateNotification method with a service error
func (suite *NotificationHandlerTestSuite) TestCreateNotification_ServiceError() {
	// Setup
	reqBody := CreateNotificationRequest{
		Description:     suite.notification.Description,
		ClickAction:     suite.notification.ClickAction,
		ClickActionURL:  suite.notification.ClickActionURL,
		IsReminder:      suite.notification.IsReminder,
		NotifTypeID:     suite.notification.NotifTypeID,
		NotifCategoryID: suite.notification.NotifCategoryID,
		Origin:          suite.notification.Origin,
	}
	reqBodyBytes, _ := json.Marshal(reqBody)
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewReader(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")

	// Add SSO ID to context
	ctx := context.WithValue(req.Context(), "ssoID", suite.notification.SSOID.String())
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	expectedError := errors.New("service error")
	suite.mockService.On("CreateNotification", req.Context(), mock.AnythingOfType("*model.Notification")).
		Return(expectedError)

	// Execute
	suite.handler.CreateNotification(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotificationByID tests the GetNotificationByID method
func (suite *NotificationHandlerTestSuite) TestGetNotificationByID() {
	// Setup
	id := suite.notification.ID
	req := httptest.NewRequest(http.MethodGet, "/notifications/"+id.String(), nil)
	w := httptest.NewRecorder()

	// Create a new router context with URL params
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("id", id.String())
	req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

	suite.mockService.On("GetNotificationByID", req.Context(), id).Return(suite.notification, nil)

	// Execute
	suite.handler.GetNotificationByID(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])

	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotificationByID_InvalidID tests the GetNotificationByID method with an invalid ID
func (suite *NotificationHandlerTestSuite) TestGetNotificationByID_InvalidID() {
	// Setup
	req := httptest.NewRequest(http.MethodGet, "/notifications/invalid-id", nil)
	w := httptest.NewRecorder()

	// Create a new router context with URL params
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("id", "invalid-id")
	req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

	// Execute
	suite.handler.GetNotificationByID(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotificationByID_NotFound tests the GetNotificationByID method with a not found error
func (suite *NotificationHandlerTestSuite) TestGetNotificationByID_NotFound() {
	// Setup
	id := suite.notification.ID
	req := httptest.NewRequest(http.MethodGet, "/notifications/"+id.String(), nil)
	w := httptest.NewRecorder()

	// Create a new router context with URL params
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("id", id.String())
	req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

	expectedError := &httpLib.RecordNotFoundException{ModelName: "Notification"}
	suite.mockService.On("GetNotificationByID", req.Context(), id).Return(nil, expectedError)

	// Execute
	suite.handler.GetNotificationByID(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotificationByID_ServiceError tests the GetNotificationByID method with a service error
func (suite *NotificationHandlerTestSuite) TestGetNotificationByID_ServiceError() {
	// Setup
	id := suite.notification.ID
	req := httptest.NewRequest(http.MethodGet, "/notifications/"+id.String(), nil)
	w := httptest.NewRecorder()

	// Create a new router context with URL params
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("id", id.String())
	req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

	expectedError := errors.New("service error")
	suite.mockService.On("GetNotificationByID", req.Context(), id).Return(nil, expectedError)

	// Execute
	suite.handler.GetNotificationByID(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotifications tests the GetNotifications method
func (suite *NotificationHandlerTestSuite) TestGetNotifications() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}

	req := httptest.NewRequest(http.MethodGet, "/notifications?page=1&limit=10", nil)
	w := httptest.NewRecorder()

	suite.mockService.On("GetNotifications", req.Context(), page, limit).Return(notifications, total, nil)

	// Execute
	suite.handler.GetNotifications(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])
	assert.NotNil(suite.T(), response["meta"])

	meta := response["meta"].(map[string]interface{})
	assert.Equal(suite.T(), float64(page), meta["page"])
	assert.Equal(suite.T(), float64(limit), meta["limit"])
	assert.Equal(suite.T(), float64(total), meta["total"])
	assert.Equal(suite.T(), float64(2), meta["total_page"]) // 20 items with 10 per page = 2 pages

	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotifications_DefaultParams tests the GetNotifications method with default parameters
func (suite *NotificationHandlerTestSuite) TestGetNotifications_DefaultParams() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}

	req := httptest.NewRequest(http.MethodGet, "/notifications", nil)
	w := httptest.NewRecorder()

	suite.mockService.On("GetNotifications", req.Context(), page, limit).Return(notifications, total, nil)

	// Execute
	suite.handler.GetNotifications(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])
	assert.NotNil(suite.T(), response["meta"])

	meta := response["meta"].(map[string]interface{})
	assert.Equal(suite.T(), float64(page), meta["page"])
	assert.Equal(suite.T(), float64(limit), meta["limit"])
	assert.Equal(suite.T(), float64(total), meta["total"])
	assert.Equal(suite.T(), float64(2), meta["total_page"]) // 20 items with 10 per page = 2 pages

	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotifications_InvalidParams tests the GetNotifications method with invalid parameters
func (suite *NotificationHandlerTestSuite) TestGetNotifications_InvalidParams() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}

	req := httptest.NewRequest(http.MethodGet, "/notifications?page=invalid&limit=invalid", nil)
	w := httptest.NewRecorder()

	suite.mockService.On("GetNotifications", req.Context(), page, limit).Return(notifications, total, nil)

	// Execute
	suite.handler.GetNotifications(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has data field
	_, hasData := response["data"]
	assert.True(suite.T(), hasData)
	assert.NotNil(suite.T(), response["data"])
	assert.NotNil(suite.T(), response["meta"])

	meta := response["meta"].(map[string]interface{})
	assert.Equal(suite.T(), float64(page), meta["page"])
	assert.Equal(suite.T(), float64(limit), meta["limit"])
	assert.Equal(suite.T(), float64(total), meta["total"])
	assert.Equal(suite.T(), float64(2), meta["total_page"]) // 20 items with 10 per page = 2 pages

	suite.mockService.AssertExpectations(suite.T())
}

// TestGetNotifications_ServiceError tests the GetNotifications method with a service error
func (suite *NotificationHandlerTestSuite) TestGetNotifications_ServiceError() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 0
	var notifications []model.Notification

	req := httptest.NewRequest(http.MethodGet, "/notifications", nil)
	w := httptest.NewRecorder()

	expectedError := errors.New("service error")
	suite.mockService.On("GetNotifications", req.Context(), page, limit).Return(notifications, total, expectedError)

	// Execute
	suite.handler.GetNotifications(w, req)

	// Assert
	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Check that the response has errors field
	_, hasErrors := response["errors"]
	assert.True(suite.T(), hasErrors)
	suite.mockService.AssertExpectations(suite.T())
}

// TestNotificationHandlerSuite runs the test suite
func TestNotificationHandlerSuite(t *testing.T) {
	suite.Run(t, new(NotificationHandlerTestSuite))
}
