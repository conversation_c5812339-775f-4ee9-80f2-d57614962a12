package repository

import (
	"context"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationTypeRepository defines the interface for notification type repository operations
type NotificationTypeRepository interface {
	Create(ctx context.Context, notificationType *model.NotificationType) error
	FindByID(ctx context.Context, id uuid.UUID) (*model.NotificationType, error)
	FindByOriginAndValue(ctx context.Context, origin model.Origin, value int) (*model.NotificationType, error)
	FindAll(ctx context.Context) ([]model.NotificationType, error)
	Update(ctx context.Context, notificationType *model.NotificationType) error
	Delete(ctx context.Context, id uuid.UUID) error
}

type notificationTypeRepository struct {
	db *gorm.DB
}

// NewNotificationTypeRepository creates a new notification type repository
func NewNotificationTypeRepository(db *gorm.DB) NotificationTypeRepository {
	return &notificationTypeRepository{
		db: db,
	}
}

// Create creates a new notification type
func (r *notificationTypeRepository) Create(ctx context.Context, notificationType *model.NotificationType) error {
	return r.db.WithContext(ctx).Create(notificationType).Error
}

// FindByID finds a notification type by ID
func (r *notificationTypeRepository) FindByID(ctx context.Context, id uuid.UUID) (*model.NotificationType, error) {
	var notificationType model.NotificationType
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&notificationType).Error
	if err != nil {
		return nil, err
	}
	return &notificationType, nil
}

// FindByOriginAndValue finds a notification type by origin and value
func (r *notificationTypeRepository) FindByOriginAndValue(ctx context.Context, origin model.Origin, value int) (*model.NotificationType, error) {
	var notificationType model.NotificationType
	err := r.db.WithContext(ctx).Where("origin = ? AND origin_value = ?", origin, value).First(&notificationType).Error
	if err != nil {
		return nil, err
	}
	return &notificationType, nil
}

// FindAll finds all notification types
func (r *notificationTypeRepository) FindAll(ctx context.Context) ([]model.NotificationType, error) {
	var notificationTypes []model.NotificationType
	err := r.db.WithContext(ctx).Find(&notificationTypes).Error
	return notificationTypes, err
}

// Update updates a notification type
func (r *notificationTypeRepository) Update(ctx context.Context, notificationType *model.NotificationType) error {
	return r.db.WithContext(ctx).Save(notificationType).Error
}

// Delete deletes a notification type
func (r *notificationTypeRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.NotificationType{}, id).Error
}
