package server

import (
	"bitbucket.org/terbang-ventures/notification-service/config"
	"bitbucket.org/terbang-ventures/notification-service/internal/docs"
	"github.com/go-chi/chi/v5"
	httpSwagger "github.com/swaggo/http-swagger"

	middlewareLib "bitbucket.org/terbang-ventures/notification-service/internal/pkg/middleware"
)

type Router struct {
	AppContext *AppContext
}

func NewRouter(ctx *AppContext) *Router {
	return &Router{
		AppContext: ctx,
	}
}

// @title Notification Service API
// @version 1.0
// @description API documentation for the Notification Service.
// @host localhost:4000
// @BasePath /api/v1
// @schemes http https
func (r *Router) RegisterRoutes(chiRouter chi.Router) {
	switch config.GetAppEnv() {
	case "production":
		docs.SwaggerInfo.Host = "notif-service.qontak.com"
	case "staging":
		docs.SwaggerInfo.Host = "notif-service.qontak.net"
	default:
		docs.SwaggerInfo.Host = "localhost" + config.GetServerAddress()
	}
	docs.SwaggerInfo.Schemes = []string{"http", "https"}
	chiRouter.Get("/swagger/*", httpSwagger.Handler())

	chiRouter.Route("/api", func(rt chi.Router) {
		r.registerV1Route(rt)
	})
}

func (r *Router) registerV1Route(chiRouter chi.Router) {
	chiRouter.Route("/v1", func(rt chi.Router) {
		rt.Use(middlewareLib.CoreAuthMiddleware())
		// Health routes
		rt.Route("/health", func(rt2 chi.Router) {
			rt2.Get("/", r.AppContext.HealthHandler.HealthCheck)
		})

		// Notification routes
		if r.AppContext.NotificationHandler != nil {
			rt.Route("/notifications", func(rt2 chi.Router) {
				rt2.Post("/", r.AppContext.NotificationHandler.CreateNotification)
				rt2.Get("/", r.AppContext.NotificationHandler.GetNotifications)
				rt2.Get("/{id}", r.AppContext.NotificationHandler.GetNotificationByID)
				rt2.Put("/mark_all_as_read", r.AppContext.NotificationHandler.MarkAllAsRead)
				rt2.Put("/mark_as_read/{id}", r.AppContext.NotificationHandler.MarkAsRead)
			})
		}

		// FCM Token routes
		if r.AppContext.FCMTokenHandler != nil {
			rt.Route("/fcm-tokens", func(rt2 chi.Router) {
				rt2.Post("/", r.AppContext.FCMTokenHandler.RegisterToken)
				rt2.Get("/sso/{sso_id}", r.AppContext.FCMTokenHandler.GetTokensBySSO)
				rt2.Delete("/{id}", r.AppContext.FCMTokenHandler.DeleteToken)
			})
		}

		// Push Notification routes
		if r.AppContext.PushNotificationHandler != nil {
			rt.Route("/push-notifications", func(rt2 chi.Router) {
				rt2.Post("/", r.AppContext.PushNotificationHandler.SendPushNotification)
			})
		}
	})
}
